import com.logictrue.config.ConfigManager;
import com.logictrue.model.ExternalApp;
import com.logictrue.model.FormField;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * 测试配置保存功能
 */
public class TestConfigSave {
    private static final Logger logger = LoggerFactory.getLogger(TestConfigSave.class);

    public static void main(String[] args) {
        logger.info("开始测试配置保存功能");
        
        ConfigManager configManager = ConfigManager.getInstance();
        
        // 设置基本配置
        configManager.setDeviceId("TEST_DEVICE_002");
        configManager.setFormName("完整测试表单");
        configManager.setApiUrl("http://localhost:8080/api/submit");
        configManager.setHeartbeatUrl("http://localhost:8080/api/heartbeat");
        configManager.setImageUrl("http://localhost:8080/api/device/image");
        
        // 创建表单字段列表
        List<FormField> formFields = new ArrayList<>();
        
        FormField field1 = new FormField();
        field1.setId("test1");
        field1.setLabel("测试字段1");
        field1.setName("test1");
        field1.setType(FormField.FieldType.TEXT);
        formFields.add(field1);
        
        FormField field2 = new FormField();
        field2.setId("test2");
        field2.setLabel("测试字段2");
        field2.setName("test2");
        field2.setType(FormField.FieldType.NUMBER);
        formFields.add(field2);
        
        // 创建外部应用程序列表
        List<ExternalApp> externalApps = new ArrayList<>();
        
        ExternalApp app1 = new ExternalApp();
        app1.setId("app1");
        app1.setName("测试应用1");
        app1.setPath("/usr/bin/notepad");
        app1.setDescription("测试应用程序1");
        externalApps.add(app1);
        
        ExternalApp app2 = new ExternalApp();
        app2.setId("app2");
        app2.setName("测试应用2");
        app2.setPath("/usr/bin/calculator");
        app2.setDescription("测试应用程序2");
        externalApps.add(app2);
        
        // 保存配置
        logger.info("保存表单字段，共{}个", formFields.size());
        configManager.setFormFields(formFields);
        
        logger.info("保存外部应用程序，共{}个", externalApps.size());
        configManager.setExternalApps(externalApps);
        
        logger.info("配置保存测试完成");
        
        // 验证保存结果
        logger.info("验证保存结果:");
        logger.info("设备ID: {}", configManager.getDeviceId());
        logger.info("表单名称: {}", configManager.getFormName());
        logger.info("表单字段数量: {}", configManager.getFormFields().size());
        logger.info("外部应用程序数量: {}", configManager.getExternalApps().size());
    }
}
