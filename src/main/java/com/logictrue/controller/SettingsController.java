package com.logictrue.controller;

import com.logictrue.config.ConfigManager;
import com.logictrue.model.ExternalApp;
import com.logictrue.model.FormField;
import com.logictrue.service.ExternalAppService;
import com.logictrue.service.NetworkService;
import javafx.application.Platform;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.stage.FileChooser;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URL;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;

/**
 * 设置界面控制器
 */
public class SettingsController implements Initializable {
    private static final Logger logger = LoggerFactory.getLogger(SettingsController.class);
    
    @FXML
    private TextField deviceIdField;
    
    @FXML
    private TextField formNameField;
    
    @FXML
    private TextField apiUrlField;
    
    @FXML
    private TextField heartbeatUrlField;
    
    @FXML
    private TextField imageUrlField;
    
    @FXML
    private Button downloadImageButton;
    
    @FXML
    private Button testHeartbeatButton;
    
    @FXML
    private Button saveButton;
    
    @FXML
    private Button cancelButton;
    
    @FXML
    private ProgressIndicator progressIndicator;
    
    @FXML
    private Label statusLabel;

    // 表单字段管理相关组件
    @FXML
    private TabPane settingsTabPane;

    @FXML
    private TableView<FormField> formFieldsTable;

    @FXML
    private TableColumn<FormField, String> labelColumn;

    @FXML
    private TableColumn<FormField, String> nameColumn;

    @FXML
    private TableColumn<FormField, String> typeColumn;

    @FXML
    private Button addFieldButton;

    @FXML
    private Button editFieldButton;

    @FXML
    private Button deleteFieldButton;

    @FXML
    private Button moveUpButton;

    @FXML
    private Button moveDownButton;

    // 外部应用程序管理相关组件
    @FXML
    private TableView<ExternalApp> externalAppsTable;

    @FXML
    private TableColumn<ExternalApp, String> appNameColumn;

    @FXML
    private TableColumn<ExternalApp, String> appPathColumn;

    @FXML
    private TableColumn<ExternalApp, String> appDescriptionColumn;

    @FXML
    private Button addAppButton;

    @FXML
    private Button editAppButton;

    @FXML
    private Button deleteAppButton;

    @FXML
    private Button launchAppButton;

    // 配置文件管理相关组件
    @FXML
    private Label configPathLabel;

    @FXML
    private Label configSizeLabel;

    @FXML
    private Label configModifiedLabel;

    @FXML
    private Button backupConfigButton;

    @FXML
    private Button restoreConfigButton;

    @FXML
    private Button autoBackupButton;

    @FXML
    private Button exportConfigButton;

    @FXML
    private Button importConfigButton;

    @FXML
    private Button resetConfigButton;

    @FXML
    private ListView<String> backupFilesList;

    @FXML
    private Button refreshBackupsButton;

    @FXML
    private Button deleteBackupButton;

    private ConfigManager configManager;
    private NetworkService networkService;
    private ExternalAppService externalAppService;
    private MainController mainController;
    private ObservableList<FormField> formFieldsList;
    private ObservableList<ExternalApp> externalAppsList;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        configManager = ConfigManager.getInstance();
        networkService = new NetworkService();
        externalAppService = new ExternalAppService();
        formFieldsList = FXCollections.observableArrayList();
        externalAppsList = FXCollections.observableArrayList();

        // 初始化界面
        initializeUI();

        // 初始化表单字段表格
        initializeFormFieldsTable();

        // 初始化外部应用程序表格
        initializeExternalAppsTable();

        // 初始化配置文件管理
        initializeConfigManagement();

        // 加载当前配置
        loadCurrentConfig();

        // 加载表单字段
        loadFormFields();

        // 加载外部应用程序
        loadExternalApps();

        // 加载配置文件信息
        loadConfigFileInfo();

        // 测试数据
        testFormFieldsData();

        logger.info("设置界面初始化完成");
    }
    
    /**
     * 初始化UI组件
     */
    private void initializeUI() {
        // 隐藏进度指示器
        progressIndicator.setVisible(false);
        
        // 绑定事件
        downloadImageButton.setOnAction(event -> downloadDeviceImage());
        testHeartbeatButton.setOnAction(event -> testHeartbeat());
        saveButton.setOnAction(event -> saveSettings());
        cancelButton.setOnAction(event -> closeWindow());
        
        // 设备ID变化时启用下载按钮
        deviceIdField.textProperty().addListener((observable, oldValue, newValue) -> {
            downloadImageButton.setDisable(newValue == null || newValue.trim().isEmpty());
        });
        
        // 心跳URL变化时启用测试按钮
        heartbeatUrlField.textProperty().addListener((observable, oldValue, newValue) -> {
            testHeartbeatButton.setDisable(newValue == null || newValue.trim().isEmpty());
        });

        // 表单字段管理按钮事件
        addFieldButton.setOnAction(event -> addFormField());
        editFieldButton.setOnAction(event -> editFormField());
        deleteFieldButton.setOnAction(event -> deleteFormField());
        moveUpButton.setOnAction(event -> moveFieldUp());
        moveDownButton.setOnAction(event -> moveFieldDown());

        // 外部应用程序管理按钮事件
        addAppButton.setOnAction(event -> addExternalApp());
        editAppButton.setOnAction(event -> editExternalApp());
        deleteAppButton.setOnAction(event -> deleteExternalApp());
        launchAppButton.setOnAction(event -> launchExternalApp());

        // 配置文件管理按钮事件
        backupConfigButton.setOnAction(event -> backupConfig());
        restoreConfigButton.setOnAction(event -> restoreConfig());
        autoBackupButton.setOnAction(event -> autoBackupConfig());
        exportConfigButton.setOnAction(event -> exportConfig());
        importConfigButton.setOnAction(event -> importConfig());
        resetConfigButton.setOnAction(event -> resetConfig());
        refreshBackupsButton.setOnAction(event -> loadBackupFiles());
        deleteBackupButton.setOnAction(event -> deleteBackupFile());
    }
    
    /**
     * 加载当前配置
     */
    private void loadCurrentConfig() {
        deviceIdField.setText(configManager.getDeviceId());
        formNameField.setText(configManager.getFormName());
        apiUrlField.setText(configManager.getApiUrl());
        heartbeatUrlField.setText(configManager.getHeartbeatUrl());
        imageUrlField.setText(configManager.getImageUrl());
        
        // 更新按钮状态
        downloadImageButton.setDisable(deviceIdField.getText().trim().isEmpty());
        testHeartbeatButton.setDisable(heartbeatUrlField.getText().trim().isEmpty());
    }
    
    /**
     * 下载设备图片
     */
    @FXML
    private void downloadDeviceImage() {
        String deviceId = deviceIdField.getText().trim();
        if (deviceId.isEmpty()) {
            showStatus("请先输入设备编号", false);
            return;
        }
        
        // 显示进度指示器
        progressIndicator.setVisible(true);
        downloadImageButton.setDisable(true);
        showStatus("正在下载设备图片...", true);
        
        Task<String> downloadTask = new Task<String>() {
            @Override
            protected String call() throws Exception {
                return networkService.downloadDeviceImage(deviceId).get();
            }
        };
        
        downloadTask.setOnSucceeded(event -> {
            Platform.runLater(() -> {
                progressIndicator.setVisible(false);
                downloadImageButton.setDisable(false);
                
                String imagePath = downloadTask.getValue();
                if (imagePath != null) {
                    configManager.setBackgroundImagePath(imagePath);
                    showStatus("设备图片下载成功", true);
                    
                    // 通知主界面更新背景图片
                    if (mainController != null) {
                        mainController.updateBackgroundImage(imagePath);
                    }
                } else {
                    showStatus("设备图片下载失败", false);
                }
            });
        });
        
        downloadTask.setOnFailed(event -> {
            Platform.runLater(() -> {
                progressIndicator.setVisible(false);
                downloadImageButton.setDisable(false);
                showStatus("设备图片下载失败: " + downloadTask.getException().getMessage(), false);
            });
        });
        
        Thread downloadThread = new Thread(downloadTask);
        downloadThread.setDaemon(true);
        downloadThread.start();
    }
    
    /**
     * 测试心跳连接
     */
    @FXML
    private void testHeartbeat() {
        String heartbeatUrl = heartbeatUrlField.getText().trim();
        if (heartbeatUrl.isEmpty()) {
            showStatus("请先输入心跳地址", false);
            return;
        }
        
        // 临时更新配置以进行测试
        String originalUrl = configManager.getHeartbeatUrl();
        configManager.setHeartbeatUrl(heartbeatUrl);
        
        progressIndicator.setVisible(true);
        testHeartbeatButton.setDisable(true);
        showStatus("正在测试心跳连接...", true);
        
        networkService.sendHeartbeat().thenAccept(result -> {
            Platform.runLater(() -> {
                progressIndicator.setVisible(false);
                testHeartbeatButton.setDisable(false);
                
                if (result.isSuccess()) {
                    showStatus(String.format("心跳测试成功 (响应时间: %dms)", result.getResponseTime()), true);
                } else {
                    showStatus(String.format("心跳测试失败 (状态码: %d)", result.getStatusCode()), false);
                }
            });
        }).exceptionally(throwable -> {
            Platform.runLater(() -> {
                progressIndicator.setVisible(false);
                testHeartbeatButton.setDisable(false);
                showStatus("心跳测试异常: " + throwable.getMessage(), false);
            });
            return null;
        });
        
        // 恢复原始配置
        configManager.setHeartbeatUrl(originalUrl);
    }
    
    /**
     * 保存设置
     */
    @FXML
    private void saveSettings() {
        try {
            // 验证输入
            if (formNameField.getText().trim().isEmpty()) {
                showStatus("表单名称不能为空", false);
                return;
            }
            
            if (apiUrlField.getText().trim().isEmpty()) {
                showStatus("接口地址不能为空", false);
                return;
            }
            
            // 保存配置 - 分步保存以避免并发问题
            configManager.setDeviceId(deviceIdField.getText().trim());
            configManager.setFormName(formNameField.getText().trim());
            configManager.setApiUrl(apiUrlField.getText().trim());
            configManager.setHeartbeatUrl(heartbeatUrlField.getText().trim());
            configManager.setImageUrl(imageUrlField.getText().trim());

            // 保存表单字段配置
            logger.info("保存表单字段配置，共{}个字段", formFieldsList.size());
            configManager.setFormFields(new ArrayList<>(formFieldsList));

            // 保存外部应用程序配置
            logger.info("保存外部应用程序配置，共{}个应用", externalAppsList.size());
            configManager.setExternalApps(new ArrayList<>(externalAppsList));

            // 通知主界面刷新外部应用程序按钮
            if (mainController != null) {
                mainController.refreshExternalAppButtons();
            }

            showStatus("设置保存成功", true);
            logger.info("设置保存成功");

            // 延迟关闭窗口
            Platform.runLater(() -> {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                closeWindow();
            });
            
        } catch (Exception e) {
            logger.error("保存设置失败", e);
            showStatus("保存设置失败: " + e.getMessage(), false);
        }
    }
    
    /**
     * 关闭窗口
     */
    @FXML
    private void closeWindow() {
        Stage stage = (Stage) cancelButton.getScene().getWindow();
        stage.close();
    }
    
    /**
     * 显示状态信息
     */
    private void showStatus(String message, boolean success) {
        statusLabel.setText(message);
        statusLabel.getStyleClass().clear();
        statusLabel.getStyleClass().add(success ? "status-success" : "status-error");
    }
    
    /**
     * 初始化表单字段表格
     */
    private void initializeFormFieldsTable() {
        // 设置表格列 - 使用lambda表达式确保正确获取属性值
        labelColumn.setCellValueFactory(cellData -> {
            FormField field = cellData.getValue();
            String label = field != null ? field.getLabel() : "";
            logger.debug("获取字段标签: {}", label);
            return new SimpleStringProperty(label);
        });

        nameColumn.setCellValueFactory(cellData -> {
            FormField field = cellData.getValue();
            String name = field != null ? field.getName() : "";
            logger.debug("获取字段名称: {}", name);
            return new SimpleStringProperty(name);
        });

        typeColumn.setCellValueFactory(cellData -> {
            FormField field = cellData.getValue();
            String typeName = field != null && field.getType() != null ?
                field.getType().getDisplayName() : "";
            logger.debug("获取字段类型: {}", typeName);
            return new SimpleStringProperty(typeName);
        });

        // 设置表格数据
        formFieldsTable.setItems(formFieldsList);

        // 表格选择事件
        formFieldsTable.getSelectionModel().selectedItemProperty().addListener((observable, oldValue, newValue) -> {
            boolean hasSelection = newValue != null;
            editFieldButton.setDisable(!hasSelection);
            deleteFieldButton.setDisable(!hasSelection);
            moveUpButton.setDisable(!hasSelection);
            moveDownButton.setDisable(!hasSelection);
        });

        logger.info("表单字段表格初始化完成");
    }

    /**
     * 加载表单字段
     */
    private void loadFormFields() {
        List<FormField> fields = configManager.getFormFields();
        logger.info("加载表单字段，共{}个字段", fields.size());
        for (FormField field : fields) {
            logger.info("字段信息: id={}, label={}, name={}, type={}",
                field.getId(), field.getLabel(), field.getName(), field.getType());
        }
        formFieldsList.clear();
        formFieldsList.addAll(fields);
        logger.info("表单字段列表更新完成，当前列表大小: {}", formFieldsList.size());

        // 强制刷新表格显示
        Platform.runLater(() -> {
            formFieldsTable.refresh();
            logger.info("表格刷新完成");
        });
    }

    /**
     * 新增表单字段
     */
    private void addFormField() {
        FormFieldEditDialog dialog = new FormFieldEditDialog(null);
        if (dialog.showAndWait()) {
            FormField newField = dialog.getFormField();
            formFieldsList.add(newField);
            showStatus("字段添加成功", true);
        }
    }

    /**
     * 编辑表单字段
     */
    private void editFormField() {
        FormField selectedField = formFieldsTable.getSelectionModel().getSelectedItem();
        if (selectedField != null) {
            FormFieldEditDialog dialog = new FormFieldEditDialog(selectedField);
            if (dialog.showAndWait()) {
                // 刷新表格显示
                formFieldsTable.refresh();
                showStatus("字段编辑成功", true);
            }
        }
    }

    /**
     * 删除表单字段
     */
    private void deleteFormField() {
        FormField selectedField = formFieldsTable.getSelectionModel().getSelectedItem();
        if (selectedField != null) {
            Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
            alert.setTitle("确认删除");
            alert.setHeaderText(null);
            alert.setContentText("确定要删除字段 \"" + selectedField.getLabel() + "\" 吗？");

            Optional<ButtonType> result = alert.showAndWait();
            if (result.isPresent() && result.get() == ButtonType.OK) {
                formFieldsList.remove(selectedField);
                showStatus("字段删除成功", true);
            }
        }
    }

    /**
     * 上移字段
     */
    private void moveFieldUp() {
        FormField selectedField = formFieldsTable.getSelectionModel().getSelectedItem();
        if (selectedField != null) {
            int currentIndex = formFieldsList.indexOf(selectedField);
            if (currentIndex > 0) {
                formFieldsList.remove(currentIndex);
                formFieldsList.add(currentIndex - 1, selectedField);
                formFieldsTable.getSelectionModel().select(currentIndex - 1);
                showStatus("字段上移成功", true);
            }
        }
    }

    /**
     * 下移字段
     */
    private void moveFieldDown() {
        FormField selectedField = formFieldsTable.getSelectionModel().getSelectedItem();
        if (selectedField != null) {
            int currentIndex = formFieldsList.indexOf(selectedField);
            if (currentIndex < formFieldsList.size() - 1) {
                formFieldsList.remove(currentIndex);
                formFieldsList.add(currentIndex + 1, selectedField);
                formFieldsTable.getSelectionModel().select(currentIndex + 1);
                showStatus("字段下移成功", true);
            }
        }
    }



    /**
     * 测试表单字段数据
     */
    private void testFormFieldsData() {
        logger.info("=== 测试表单字段数据 ===");
        logger.info("formFieldsList大小: {}", formFieldsList.size());
        for (int i = 0; i < formFieldsList.size(); i++) {
            FormField field = formFieldsList.get(i);
            logger.info("字段[{}]: id={}, label={}, name={}, type={}",
                i, field.getId(), field.getLabel(), field.getName(),
                field.getType() != null ? field.getType().getDisplayName() : "null");
        }
        logger.info("=== 测试完成 ===");
    }

    /**
     * 初始化外部应用程序表格
     */
    private void initializeExternalAppsTable() {
        // 设置表格列使用回调函数
        appNameColumn.setCellValueFactory(cellData -> {
            ExternalApp app = cellData.getValue();
            return new SimpleStringProperty(app != null ? app.getName() : "");
        });

        appPathColumn.setCellValueFactory(cellData -> {
            ExternalApp app = cellData.getValue();
            return new SimpleStringProperty(app != null ? app.getPath() : "");
        });

        appDescriptionColumn.setCellValueFactory(cellData -> {
            ExternalApp app = cellData.getValue();
            return new SimpleStringProperty(app != null ? app.getDescription() : "");
        });

        // 设置列的可调整大小
        appNameColumn.setResizable(true);
        appPathColumn.setResizable(true);
        appDescriptionColumn.setResizable(true);

        // 设置表格数据
        externalAppsTable.setItems(externalAppsList);

        // 设置表格属性
        externalAppsTable.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY);

        // 表格选择事件
        externalAppsTable.getSelectionModel().selectedItemProperty().addListener((observable, oldValue, newValue) -> {
            boolean hasSelection = newValue != null;
            editAppButton.setDisable(!hasSelection);
            deleteAppButton.setDisable(!hasSelection);
            launchAppButton.setDisable(!hasSelection);
        });

        logger.info("外部应用程序表格初始化完成");
    }

    /**
     * 初始化配置文件管理
     */
    private void initializeConfigManagement() {
        // 备份文件列表选择事件
        backupFilesList.getSelectionModel().selectedItemProperty().addListener((observable, oldValue, newValue) -> {
            deleteBackupButton.setDisable(newValue == null);
        });

        logger.info("配置文件管理初始化完成");
    }

    /**
     * 加载外部应用程序
     */
    private void loadExternalApps() {
        List<ExternalApp> apps = configManager.getExternalApps();
        logger.info("加载外部应用程序，共{}个应用", apps.size());
        externalAppsList.clear();
        externalAppsList.addAll(apps);

        // 刷新表格显示
        Platform.runLater(() -> {
            externalAppsTable.refresh();
        });
    }

    /**
     * 加载配置文件信息
     */
    private void loadConfigFileInfo() {
        try {
            String configPath = configManager.getConfigFilePath();
            java.io.File configFile = new java.io.File(configPath);

            configPathLabel.setText("配置文件路径: " + configPath);

            if (configFile.exists()) {
                long fileSize = configFile.length();
                String sizeText = formatFileSize(fileSize);
                configSizeLabel.setText("文件大小: " + sizeText);

                java.util.Date lastModified = new java.util.Date(configFile.lastModified());
                configModifiedLabel.setText("最后修改: " + lastModified);
            } else {
                configSizeLabel.setText("文件大小: 文件不存在");
                configModifiedLabel.setText("最后修改: 未知");
            }

            // 加载备份文件列表
            loadBackupFiles();

        } catch (Exception e) {
            logger.error("加载配置文件信息失败", e);
        }
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        }
    }

    /**
     * 新增外部应用程序
     */
    private void addExternalApp() {
        ExternalAppEditDialog dialog = new ExternalAppEditDialog(null);
        if (dialog.showAndWait()) {
            ExternalApp newApp = dialog.getExternalApp();
            externalAppsList.add(newApp);

            // 刷新表格显示
            externalAppsTable.refresh();

            // 选中新添加的应用
            externalAppsTable.getSelectionModel().select(newApp);

            showStatus("应用程序添加成功", true);
            logger.info("新增外部应用程序: {}", newApp);
        }
    }

    /**
     * 编辑外部应用程序
     */
    private void editExternalApp() {
        ExternalApp selectedApp = externalAppsTable.getSelectionModel().getSelectedItem();
        if (selectedApp != null) {
            ExternalAppEditDialog dialog = new ExternalAppEditDialog(selectedApp);
            if (dialog.showAndWait()) {
                // 刷新表格显示
                externalAppsTable.refresh();
                showStatus("应用程序编辑成功", true);
            }
        }
    }

    /**
     * 删除外部应用程序
     */
    private void deleteExternalApp() {
        ExternalApp selectedApp = externalAppsTable.getSelectionModel().getSelectedItem();
        if (selectedApp != null) {
            Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
            alert.setTitle("确认删除");
            alert.setHeaderText(null);
            alert.setContentText("确定要删除应用程序 \"" + selectedApp.getName() + "\" 吗？");

            Optional<ButtonType> result = alert.showAndWait();
            if (result.isPresent() && result.get() == ButtonType.OK) {
                externalAppsList.remove(selectedApp);
                showStatus("应用程序删除成功", true);
            }
        }
    }

    /**
     * 启动外部应用程序
     */
    private void launchExternalApp() {
        ExternalApp selectedApp = externalAppsTable.getSelectionModel().getSelectedItem();
        if (selectedApp != null) {
            launchAppButton.setDisable(true);
            launchAppButton.setText("启动中...");

            externalAppService.launchApp(selectedApp).thenAccept(success -> {
                Platform.runLater(() -> {
                    launchAppButton.setDisable(false);
                    launchAppButton.setText("启动应用");

                    if (success) {
                        showStatus("应用程序启动成功: " + selectedApp.getName(), true);
                    } else {
                        showStatus("应用程序启动失败: " + selectedApp.getName(), false);
                    }
                });
            });
        }
    }

    /**
     * 备份配置文件
     */
    private void backupConfig() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("保存配置备份");
        fileChooser.setInitialFileName("config_backup.json");
        fileChooser.getExtensionFilters().add(
            new FileChooser.ExtensionFilter("JSON文件", "*.json")
        );

        java.io.File file = fileChooser.showSaveDialog(backupConfigButton.getScene().getWindow());
        if (file != null) {
            boolean success = configManager.backupConfig(file.getAbsolutePath());
            showStatus(success ? "配置备份成功" : "配置备份失败", success);
            if (success) {
                loadBackupFiles();
            }
        }
    }

    /**
     * 恢复配置文件
     */
    private void restoreConfig() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("选择配置备份文件");
        fileChooser.getExtensionFilters().add(
            new FileChooser.ExtensionFilter("JSON文件", "*.json")
        );

        java.io.File file = fileChooser.showOpenDialog(restoreConfigButton.getScene().getWindow());
        if (file != null) {
            Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
            confirmAlert.setTitle("确认恢复");
            confirmAlert.setHeaderText("恢复配置文件");
            confirmAlert.setContentText("恢复配置将覆盖当前所有设置，是否继续？");

            Optional<ButtonType> result = confirmAlert.showAndWait();
            if (result.isPresent() && result.get() == ButtonType.OK) {
                boolean success = configManager.restoreConfig(file.getAbsolutePath());
                if (success) {
                    showStatus("配置恢复成功，请重新打开设置界面", true);
                    // 重新加载所有配置
                    loadCurrentConfig();
                    loadFormFields();
                    loadExternalApps();
                    loadConfigFileInfo();
                } else {
                    showStatus("配置恢复失败", false);
                }
            }
        }
    }

    /**
     * 自动备份配置文件
     */
    private void autoBackupConfig() {
        String backupPath = configManager.autoBackupConfig();
        if (backupPath != null) {
            showStatus("自动备份成功: " + new java.io.File(backupPath).getName(), true);
            loadBackupFiles();
        } else {
            showStatus("自动备份失败", false);
        }
    }

    /**
     * 导出配置文件
     */
    private void exportConfig() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("导出配置文件");
        fileChooser.setInitialFileName("iot-config-export.json");
        fileChooser.getExtensionFilters().add(
            new FileChooser.ExtensionFilter("JSON文件", "*.json")
        );

        java.io.File file = fileChooser.showSaveDialog(exportConfigButton.getScene().getWindow());
        if (file != null) {
            boolean success = configManager.exportConfig(file.getAbsolutePath());
            showStatus(success ? "配置导出成功" : "配置导出失败", success);
        }
    }

    /**
     * 导入配置文件
     */
    private void importConfig() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("导入配置文件");
        fileChooser.getExtensionFilters().add(
            new FileChooser.ExtensionFilter("JSON文件", "*.json")
        );

        java.io.File file = fileChooser.showOpenDialog(importConfigButton.getScene().getWindow());
        if (file != null) {
            Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
            confirmAlert.setTitle("确认导入");
            confirmAlert.setHeaderText("导入配置文件");
            confirmAlert.setContentText("导入配置将覆盖当前所有设置，是否继续？");

            Optional<ButtonType> result = confirmAlert.showAndWait();
            if (result.isPresent() && result.get() == ButtonType.OK) {
                boolean success = configManager.importConfig(file.getAbsolutePath());
                if (success) {
                    showStatus("配置导入成功，请重新打开设置界面", true);
                    // 重新加载所有配置
                    loadCurrentConfig();
                    loadFormFields();
                    loadExternalApps();
                    loadConfigFileInfo();
                } else {
                    showStatus("配置导入失败", false);
                }
            }
        }
    }

    /**
     * 重置配置文件
     */
    private void resetConfig() {
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("确认重置");
        confirmAlert.setHeaderText("重置配置文件");
        confirmAlert.setContentText("重置将删除所有配置并恢复默认设置，是否继续？");

        Optional<ButtonType> result = confirmAlert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            try {
                // 先备份当前配置
                configManager.autoBackupConfig();

                // 删除配置文件
                java.io.File configFile = new java.io.File(configManager.getConfigFilePath());
                if (configFile.exists()) {
                    configFile.delete();
                }

                // 重新创建默认配置
                configManager = ConfigManager.getInstance();

                showStatus("配置重置成功，请重新打开设置界面", true);

                // 重新加载所有配置
                loadCurrentConfig();
                loadFormFields();
                loadExternalApps();
                loadConfigFileInfo();

            } catch (Exception e) {
                logger.error("重置配置失败", e);
                showStatus("重置配置失败: " + e.getMessage(), false);
            }
        }
    }

    /**
     * 加载备份文件列表
     */
    private void loadBackupFiles() {
        try {
            java.io.File configDir = new java.io.File(configManager.getConfigDir());
            if (configDir.exists() && configDir.isDirectory()) {
                java.io.File[] backupFiles = configDir.listFiles((dir, name) ->
                    name.startsWith("config_backup_") && name.endsWith(".json"));

                backupFilesList.getItems().clear();
                if (backupFiles != null) {
                    for (java.io.File file : backupFiles) {
                        backupFilesList.getItems().add(file.getName());
                    }
                }
            }
        } catch (Exception e) {
            logger.error("加载备份文件列表失败", e);
        }
    }

    /**
     * 删除备份文件
     */
    private void deleteBackupFile() {
        String selectedBackup = backupFilesList.getSelectionModel().getSelectedItem();
        if (selectedBackup != null) {
            Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
            confirmAlert.setTitle("确认删除");
            confirmAlert.setHeaderText(null);
            confirmAlert.setContentText("确定要删除备份文件 \"" + selectedBackup + "\" 吗？");

            Optional<ButtonType> result = confirmAlert.showAndWait();
            if (result.isPresent() && result.get() == ButtonType.OK) {
                try {
                    java.io.File backupFile = new java.io.File(configManager.getConfigDir(), selectedBackup);
                    if (backupFile.delete()) {
                        showStatus("备份文件删除成功", true);
                        loadBackupFiles();
                    } else {
                        showStatus("备份文件删除失败", false);
                    }
                } catch (Exception e) {
                    logger.error("删除备份文件失败", e);
                    showStatus("删除备份文件失败: " + e.getMessage(), false);
                }
            }
        }
    }

    /**
     * 设置主控制器引用
     */
    public void setMainController(MainController mainController) {
        this.mainController = mainController;
    }
}
